//
//  ServerConnection.swift
//  Ambient AI Peripheral
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import Foundation
import Combine

// MARK: - Connection State
enum ConnectionState {
    case disconnected
    case connecting
    case connected
    case reconnecting
    case error(Error)
}

// MARK: - Server Connection Manager
class ServerConnection: ObservableObject {
    
    // MARK: - Published Properties
    @Published var connectionState: ConnectionState = .disconnected
    @Published var isConnected: Bool = false
    
    // MARK: - Callback Properties
    var onTranscriptChunkReceived: ((Transcript_TranscriptChunk) -> Void)?
    var onConnectionStateChanged: ((ConnectionState) -> Void)?
    
    // MARK: - Private Properties
    private var webSocketTask: URLSessionWebSocketTask?
    private var urlSession: URLSession
    private var serverURL: URL?
    private var reconnectAttempts = 0
    private let maxReconnectAttempts = 5
    private let reconnectDelay: TimeInterval = 2.0
    private var isReconnecting = false
    
    // MARK: - Initialization
    init() {
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 10
        config.waitsForConnectivity = true
        self.urlSession = URLSession(configuration: config)
    }
    
    // MARK: - Public Methods
    
    /// Connect to the WebSocket server
    func connect(to url: URL) async throws {
        await disconnect()
        
        self.serverURL = url
        self.reconnectAttempts = 0
        
        try await performConnection()
    }
    
    /// Disconnect from the WebSocket server
    func disconnect() async {
        await MainActor.run {
            connectionState = .disconnected
            isConnected = false
            isReconnecting = false
        }
        
        webSocketTask?.cancel(with: .goingAway, reason: nil)
        webSocketTask = nil
        
        onConnectionStateChanged?(connectionState)
    }
    
    /// Send a TranscriptChunk to the server
    func send(chunk: Transcript_TranscriptChunk) async throws {
        guard isConnected, let webSocketTask = webSocketTask else {
            throw ServerConnectionError.notConnected
        }
        
        do {
            let data = try chunk.serializedData()
            try await webSocketTask.send(.data(data))
        } catch {
            print("Error sending chunk: \(error)")
            throw error
        }
    }
    
    /// Convenience method to send data with automatic TranscriptChunk creation
    func send(chunkType: Transcript_ChunkType, payload: Data, clientTimestamps: (start: Int64, end: Int64)) async throws {
        var chunk = Transcript_TranscriptChunk()
        chunk.type = chunkType
        chunk.clientTimestampStart = clientTimestamps.start
        chunk.clientTimestampEnd = clientTimestamps.end
        chunk.payload = payload
        
        try await send(chunk: chunk)
    }
    
    // MARK: - Private Methods
    
    private func performConnection() async throws {
        guard let serverURL = serverURL else {
            print("❌ ServerConnection: Invalid URL")
            throw ServerConnectionError.invalidURL
        }
        
        print("🔄 ServerConnection: Starting connection to \(serverURL)")
        
        await MainActor.run {
            connectionState = .connecting
            isConnected = false
        }
        onConnectionStateChanged?(connectionState)
        
        // Create WebSocket task
        webSocketTask = urlSession.webSocketTask(with: serverURL)
        print("📡 ServerConnection: Created WebSocket task")
        
        // Start the connection
        webSocketTask?.resume()
        print("▶️ ServerConnection: WebSocket task resumed")
        
        // Try to send a ping to verify connection with timeout
        do {
            guard let webSocketTask = webSocketTask else {
                print("❌ ServerConnection: WebSocket task is nil")
                throw ServerConnectionError.connectionFailed
            }
            
            // If ping succeeds, we're connected - now start listening
            await MainActor.run {
                connectionState = .connected
                isConnected = true
                reconnectAttempts = 0
            }
            onConnectionStateChanged?(connectionState)
            
            // Start listening for messages
            Task {
                await startListening()
            }
            
            let testMessage = Transcript_TranscriptChunk.imu(Transcript_IMUData.from(accel: (0, 1, 2), gyro: (0.0, 1.0, 2.0), gravity: (9.8, 0.0, 0.0)), clientStart: 1, clientEnd: 2)
            try! await webSocketTask.send(.data(try! testMessage.serializedData()))
            try! await webSocketTask.send(.data(try! testMessage.serializedData()))
            
            print("✅ Connected to WebSocket server at \(serverURL)")
        } catch {
            // If ping fails, clean up and throw error
            print("❌ ServerConnection: Ping failed with error: \(error)")
            print("❌ ServerConnection: Connection failed - cleaning up")
            webSocketTask?.cancel()
            webSocketTask = nil
            
            await MainActor.run {
                connectionState = .error(error)
                isConnected = false
            }
            onConnectionStateChanged?(connectionState)
            
            print("❌ Failed to connect to WebSocket server: \(error.localizedDescription)")
            throw ServerConnectionError.connectionFailed
        }
    }
    
    private func startListening() async {
        guard let webSocketTask = webSocketTask else { 
            print("❌ StartListening: WebSocket task is nil")
            return 
        }
        
        print("👂 ServerConnection: Starting to listen for messages (isConnected: \(isConnected))")
        
        while !Task.isCancelled {
            // Check if we're still connected before trying to receive
            guard isConnected else {
                print("⏹️ ServerConnection: Stopping listener - not connected (isConnected: \(isConnected))")
                break
            }
            
            print("📡 ServerConnection: Waiting for message...")
            
            do {
                let message = try await webSocketTask.receive()
                print("📨 ServerConnection: Received message")
                await handleMessage(message)
            } catch {
                print("❌ ServerConnection: WebSocket receive error: \(error)")
                
                // Only handle as connection error if we were actually connected
                if isConnected {
                    print("🔄 ServerConnection: Handling connection error since we were connected")
                    await handleConnectionError(error)
                } else {
                    print("ℹ️ ServerConnection: Ignoring error since we weren't connected")
                }
                break
            }
        }
        
        print("🛑 ServerConnection: WebSocket listener stopped")
    }
    
    private func handleMessage(_ message: URLSessionWebSocketTask.Message) async {
        switch message {
        case .data(let data):
            await handleDataMessage(data)
        case .string(let string):
            print("Received string message: \(string)")
        @unknown default:
            print("Received unknown message type")
        }
    }
    
    private func handleDataMessage(_ data: Data) async {
        do {
            let chunk = try Transcript_TranscriptChunk.from(data: data)
            
            // Call the callback on the main thread
            await MainActor.run {
                onTranscriptChunkReceived?(chunk)
            }
        } catch {
            print("Error deserializing TranscriptChunk: \(error)")
        }
    }
    
    private func handleConnectionError(_ error: Error) async {
        print("WebSocket connection error: \(error)")
        
        await MainActor.run {
            connectionState = .error(error)
            isConnected = false
        }
        onConnectionStateChanged?(connectionState)
        
        // Attempt reconnection if not manually disconnected
        if !isReconnecting && reconnectAttempts < maxReconnectAttempts {
            await attemptReconnection()
        }
    }
    
    private func attemptReconnection() async {
        guard !isReconnecting else { return }
        
        await MainActor.run {
            isReconnecting = true
            reconnectAttempts += 1
            connectionState = .reconnecting
        }
        onConnectionStateChanged?(connectionState)
        
        print("Attempting reconnection (\(reconnectAttempts)/\(maxReconnectAttempts))...")
        
        // Wait before reconnecting
        try? await Task.sleep(nanoseconds: UInt64(reconnectDelay * 1_000_000_000))
        
        do {
            try await performConnection()
            await MainActor.run {
                isReconnecting = false
            }
        } catch {
            print("Reconnection attempt failed: \(error)")
            await MainActor.run {
                isReconnecting = false
            }
            
            if reconnectAttempts >= maxReconnectAttempts {
                await MainActor.run {
                    connectionState = .error(ServerConnectionError.maxReconnectAttemptsReached)
                }
                onConnectionStateChanged?(connectionState)
            } else {
                // Try again
                await attemptReconnection()
            }
        }
    }
}

// MARK: - Error Types
enum ServerConnectionError: LocalizedError {
    case notConnected
    case invalidURL
    case connectionFailed
    case connectionTimeout
    case maxReconnectAttemptsReached
    
    var errorDescription: String? {
        switch self {
        case .notConnected:
            return "Not connected to server"
        case .invalidURL:
            return "Invalid server URL"
        case .connectionFailed:
            return "Failed to connect to server"
        case .connectionTimeout:
            return "Connection timeout - server may be unreachable"
        case .maxReconnectAttemptsReached:
            return "Maximum reconnection attempts reached"
        }
    }
}

// MARK: - Extension for Default Server URL
extension ServerConnection {
    /// Default server URL (can be overridden)
    static let defaultServerURL = URL(string: "ws://************:8000/ws")!
    
    /// Connect to the default server
    func connectToDefaultServer() async throws {
        try await connect(to: Self.defaultServerURL)
    }
}
