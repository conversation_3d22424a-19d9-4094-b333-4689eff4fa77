"""
Voice Activity Detection state machine.

This module implements a state machine for tracking speech start and end events
based on VAD predictions, with configurable silence thresholds.
"""

import time
import logging
from enum import Enum
from typing import Optional, Callable

logger = logging.getLogger(__name__)


class SpeechState(Enum):
    """Speech detection states."""
    SILENCE = "silence"
    SPEECH = "speech"
    PENDING_END = "pending_end"  # Waiting for silence confirmation


class VADStateMachine:
    """
    State machine for voice activity detection.
    
    Tracks speech start and end events with configurable silence thresholds
    to avoid false positives from brief pauses.
    """
    
    def __init__(
        self,
        silence_threshold_ms: int = 800,  # 0.8 seconds of silence to end speech
        speech_threshold_ms: int = 200,   # 0.2 seconds of speech to start
        chunk_duration_ms: int = 200      # Duration of each audio chunk
    ):
        """
        Initialize the VAD state machine.
        
        Args:
            silence_threshold_ms: Milliseconds of silence needed to end speech
            speech_threshold_ms: Milliseconds of speech needed to start
            chunk_duration_ms: Duration of each audio chunk in milliseconds
        """
        self.silence_threshold_ms = silence_threshold_ms
        self.speech_threshold_ms = speech_threshold_ms
        self.chunk_duration_ms = chunk_duration_ms
        
        # Calculate thresholds in chunks
        self.silence_threshold_chunks = silence_threshold_ms // chunk_duration_ms
        self.speech_threshold_chunks = speech_threshold_ms // chunk_duration_ms
        
        # State tracking
        self.current_state = SpeechState.SILENCE
        self.silence_chunk_count = 0
        self.speech_chunk_count = 0
        self.last_state_change = time.time()
        
        # Callbacks
        self.on_speech_start: Optional[Callable] = None
        self.on_speech_end: Optional[Callable] = None
        
        logger.info(
            f"VADStateMachine initialized: "
            f"silence_threshold={silence_threshold_ms}ms ({self.silence_threshold_chunks} chunks), "
            f"speech_threshold={speech_threshold_ms}ms ({self.speech_threshold_chunks} chunks)"
        )
    
    def set_callbacks(
        self, 
        on_speech_start: Optional[Callable] = None,
        on_speech_end: Optional[Callable] = None
    ):
        """
        Set callback functions for speech events.
        
        Args:
            on_speech_start: Called when speech starts
            on_speech_end: Called when speech ends
        """
        self.on_speech_start = on_speech_start
        self.on_speech_end = on_speech_end
    
    def update(self, has_speech: bool) -> None:
        """
        Update the state machine with a new VAD prediction.
        
        Args:
            has_speech: True if speech is detected in current chunk
        """
        previous_state = self.current_state
        
        if has_speech:
            self.speech_chunk_count += 1
            self.silence_chunk_count = 0
        else:
            self.silence_chunk_count += 1
            self.speech_chunk_count = 0
        
        # State transitions
        if self.current_state == SpeechState.SILENCE:
            if self.speech_chunk_count >= self.speech_threshold_chunks:
                self._transition_to_speech()
        
        elif self.current_state == SpeechState.SPEECH:
            if not has_speech:
                self._transition_to_pending_end()
        
        elif self.current_state == SpeechState.PENDING_END:
            if has_speech:
                # Speech resumed, go back to speech state
                self._transition_to_speech()
            elif self.silence_chunk_count >= self.silence_threshold_chunks:
                # Enough silence, end speech
                self._transition_to_silence()
        
        # Log state changes
        if previous_state != self.current_state:
            logger.info(
                f"State transition: {previous_state.value} -> {self.current_state.value} "
                f"(speech_chunks: {self.speech_chunk_count}, silence_chunks: {self.silence_chunk_count})"
            )
    
    def _transition_to_speech(self):
        """Transition to speech state."""
        self.current_state = SpeechState.SPEECH
        self.last_state_change = time.time()
        self.silence_chunk_count = 0
        
        if self.on_speech_start:
            try:
                self.on_speech_start()
            except Exception as e:
                logger.error(f"Error in speech start callback: {e}")
    
    def _transition_to_pending_end(self):
        """Transition to pending end state."""
        self.current_state = SpeechState.PENDING_END
        self.last_state_change = time.time()
        self.speech_chunk_count = 0
    
    def _transition_to_silence(self):
        """Transition to silence state."""
        self.current_state = SpeechState.SILENCE
        self.last_state_change = time.time()
        self.speech_chunk_count = 0
        
        if self.on_speech_end:
            try:
                self.on_speech_end()
            except Exception as e:
                logger.error(f"Error in speech end callback: {e}")
    
    def is_speaking(self) -> bool:
        """
        Check if currently in a speaking state.
        
        Returns:
            True if in SPEECH or PENDING_END state
        """
        return self.current_state in [SpeechState.SPEECH, SpeechState.PENDING_END]
    
    def get_current_state(self) -> SpeechState:
        """Get the current state."""
        return self.current_state
    
    def get_time_in_current_state(self) -> float:
        """
        Get time spent in current state.
        
        Returns:
            Time in seconds
        """
        return time.time() - self.last_state_change
    
    def get_stats(self) -> dict:
        """
        Get state machine statistics.
        
        Returns:
            Dictionary with current state and counters
        """
        return {
            "current_state": self.current_state.value,
            "speech_chunk_count": self.speech_chunk_count,
            "silence_chunk_count": self.silence_chunk_count,
            "time_in_current_state": self.get_time_in_current_state(),
            "silence_threshold_chunks": self.silence_threshold_chunks,
            "speech_threshold_chunks": self.speech_threshold_chunks
        }
    
    def reset(self):
        """Reset the state machine to initial state."""
        self.current_state = SpeechState.SILENCE
        self.silence_chunk_count = 0
        self.speech_chunk_count = 0
        self.last_state_change = time.time()
        logger.info("VAD state machine reset")
