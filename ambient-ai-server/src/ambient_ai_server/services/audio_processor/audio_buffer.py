"""
Audio buffering utilities for VAD-based transcription.

This module provides audio buffer management for both short rolling buffers
used for VAD analysis and longer speech buffers for accumulating complete utterances.
"""

import numpy as np
from typing import Optional
from collections import deque
import logging

logger = logging.getLogger(__name__)


class AudioBuffer:
    """
    Manages audio buffering for VAD and transcription.
    
    Maintains two buffers:
    1. Rolling buffer: Short buffer (3-5 seconds) for VAD analysis
    2. Speech buffer: Longer buffer that accumulates audio during speech
    """
    
    def __init__(
        self, 
        sample_rate: int = 48000,
        rolling_buffer_duration: float = 4.0,  # 4 seconds
        chunk_duration_ms: int = 200  # 200ms chunks
    ):
        """
        Initialize the audio buffer.
        
        Args:
            sample_rate: Audio sample rate in Hz
            rolling_buffer_duration: Duration of rolling buffer in seconds
            chunk_duration_ms: Duration of each audio chunk in milliseconds
        """
        self.sample_rate = sample_rate
        self.rolling_buffer_duration = rolling_buffer_duration
        self.chunk_duration_ms = chunk_duration_ms
        
        # Calculate buffer sizes
        self.chunk_samples = int(sample_rate * chunk_duration_ms / 1000)
        self.rolling_buffer_samples = int(sample_rate * rolling_buffer_duration)
        self.max_rolling_chunks = int(rolling_buffer_duration * 1000 / chunk_duration_ms)
        
        # Initialize buffers
        self.rolling_buffer = deque(maxlen=self.max_rolling_chunks)
        self.speech_buffer = []
        
        logger.info(
            f"AudioBuffer initialized: {sample_rate}Hz, "
            f"rolling buffer: {rolling_buffer_duration}s ({self.max_rolling_chunks} chunks), "
            f"chunk size: {chunk_duration_ms}ms ({self.chunk_samples} samples)"
        )
    
    def add_chunk(self, pcm_data: bytes) -> None:
        """
        Add a new audio chunk to both buffers.
        
        Args:
            pcm_data: Raw PCM audio data (16-bit LE)
        """
        # Convert bytes to numpy array
        audio_array = np.frombuffer(pcm_data, dtype=np.int16)
        
        # Add to rolling buffer (automatically maintains max size)
        self.rolling_buffer.append(audio_array)
        
        # Add to speech buffer (unlimited during speech)
        self.speech_buffer.append(audio_array)
        
        logger.debug(
            f"Added chunk: {len(audio_array)} samples, "
            f"rolling buffer: {len(self.rolling_buffer)} chunks, "
            f"speech buffer: {len(self.speech_buffer)} chunks"
        )
    
    def get_rolling_buffer_audio(self) -> Optional[np.ndarray]:
        """
        Get the current rolling buffer as a single audio array.
        
        Returns:
            Concatenated audio array or None if buffer is empty
        """
        if not self.rolling_buffer:
            return None
        
        # Concatenate all chunks in rolling buffer
        audio_array = np.concatenate(list(self.rolling_buffer))
        return audio_array
    
    def get_speech_buffer_audio(self) -> Optional[np.ndarray]:
        """
        Get the current speech buffer as a single audio array.
        
        Returns:
            Concatenated audio array or None if buffer is empty
        """
        if not self.speech_buffer:
            return None
        
        # Concatenate all chunks in speech buffer
        audio_array = np.concatenate(self.speech_buffer)
        return audio_array
    
    def clear_speech_buffer(self) -> None:
        """Clear the speech buffer."""
        self.speech_buffer.clear()
        logger.debug("Speech buffer cleared")
    
    def get_speech_buffer_duration(self) -> float:
        """
        Get the duration of audio in the speech buffer.
        
        Returns:
            Duration in seconds
        """
        if not self.speech_buffer:
            return 0.0
        
        total_samples = sum(len(chunk) for chunk in self.speech_buffer)
        return total_samples / self.sample_rate
    
    def get_rolling_buffer_duration(self) -> float:
        """
        Get the duration of audio in the rolling buffer.
        
        Returns:
            Duration in seconds
        """
        if not self.rolling_buffer:
            return 0.0
        
        total_samples = sum(len(chunk) for chunk in self.rolling_buffer)
        return total_samples / self.sample_rate
    
    def get_stats(self) -> dict:
        """
        Get buffer statistics.
        
        Returns:
            Dictionary with buffer statistics
        """
        return {
            "rolling_buffer_chunks": len(self.rolling_buffer),
            "rolling_buffer_duration": self.get_rolling_buffer_duration(),
            "speech_buffer_chunks": len(self.speech_buffer),
            "speech_buffer_duration": self.get_speech_buffer_duration(),
            "sample_rate": self.sample_rate,
            "chunk_duration_ms": self.chunk_duration_ms
        }
