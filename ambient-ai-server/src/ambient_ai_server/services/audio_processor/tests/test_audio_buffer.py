#!/usr/bin/env python3
"""
Tests for the audio buffer.
"""

import numpy as np
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent.parent))

from ambient_ai_server.services.audio_processor.audio_buffer import AudioBuffer


def test_audio_buffer_initialization():
    """Test AudioBuffer initialization."""
    buffer = AudioBuffer(sample_rate=48000, rolling_buffer_duration=4.0)
    assert buffer.sample_rate == 48000
    assert buffer.rolling_buffer_duration == 4.0
    assert buffer.chunk_duration_ms == 200
    assert len(buffer.rolling_buffer) == 0
    assert len(buffer.speech_buffer) == 0


def test_add_chunk():
    """Test adding audio chunks."""
    buffer = AudioBuffer(sample_rate=48000, rolling_buffer_duration=1.0)  # 1 second for testing
    
    # Create test audio chunk (200ms at 48kHz = 9600 samples)
    chunk_samples = int(48000 * 0.2)  # 200ms
    test_audio = np.random.randint(-32768, 32767, chunk_samples, dtype=np.int16)
    pcm_data = test_audio.tobytes()
    
    # Add chunk
    buffer.add_chunk(pcm_data)
    
    assert len(buffer.rolling_buffer) == 1
    assert len(buffer.speech_buffer) == 1
    assert buffer.get_rolling_buffer_duration() > 0
    assert buffer.get_speech_buffer_duration() > 0


def test_rolling_buffer_max_size():
    """Test that rolling buffer respects max size."""
    buffer = AudioBuffer(sample_rate=48000, rolling_buffer_duration=0.4)  # 0.4s = 2 chunks max
    
    chunk_samples = int(48000 * 0.2)  # 200ms
    test_audio = np.random.randint(-32768, 32767, chunk_samples, dtype=np.int16)
    pcm_data = test_audio.tobytes()
    
    # Add 5 chunks
    for i in range(5):
        buffer.add_chunk(pcm_data)
    
    # Rolling buffer should only have 2 chunks (max size)
    assert len(buffer.rolling_buffer) == 2
    # Speech buffer should have all 5 chunks
    assert len(buffer.speech_buffer) == 5


def test_clear_speech_buffer():
    """Test clearing speech buffer."""
    buffer = AudioBuffer()
    
    chunk_samples = int(48000 * 0.2)
    test_audio = np.random.randint(-32768, 32767, chunk_samples, dtype=np.int16)
    pcm_data = test_audio.tobytes()
    
    buffer.add_chunk(pcm_data)
    assert len(buffer.speech_buffer) == 1
    
    buffer.clear_speech_buffer()
    assert len(buffer.speech_buffer) == 0
    # Rolling buffer should remain
    assert len(buffer.rolling_buffer) == 1


def test_get_audio_arrays():
    """Test getting audio arrays from buffers."""
    buffer = AudioBuffer()
    
    chunk_samples = int(48000 * 0.2)
    test_audio1 = np.random.randint(-32768, 32767, chunk_samples, dtype=np.int16)
    test_audio2 = np.random.randint(-32768, 32767, chunk_samples, dtype=np.int16)
    
    buffer.add_chunk(test_audio1.tobytes())
    buffer.add_chunk(test_audio2.tobytes())
    
    rolling_audio = buffer.get_rolling_buffer_audio()
    speech_audio = buffer.get_speech_buffer_audio()
    
    assert rolling_audio is not None
    assert speech_audio is not None
    assert len(rolling_audio) == chunk_samples * 2
    assert len(speech_audio) == chunk_samples * 2


def test_buffer_stats():
    """Test buffer statistics."""
    buffer = AudioBuffer()
    
    chunk_samples = int(48000 * 0.2)
    test_audio = np.random.randint(-32768, 32767, chunk_samples, dtype=np.int16)
    
    buffer.add_chunk(test_audio.tobytes())
    
    stats = buffer.get_stats()
    assert "rolling_buffer_chunks" in stats
    assert "speech_buffer_chunks" in stats
    assert "rolling_buffer_duration" in stats
    assert "speech_buffer_duration" in stats
    assert stats["rolling_buffer_chunks"] == 1
    assert stats["speech_buffer_chunks"] == 1


if __name__ == "__main__":
    test_audio_buffer_initialization()
    test_add_chunk()
    test_rolling_buffer_max_size()
    test_clear_speech_buffer()
    test_get_audio_arrays()
    test_buffer_stats()
    print("All audio buffer tests passed!")
