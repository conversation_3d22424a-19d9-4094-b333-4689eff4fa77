"""
Audio processor for VAD-based transcription.

This module orchestrates voice activity detection, audio buffering, and
Whisper transcription to provide sentence-level transcription of continuous audio.
"""

import asyncio
import logging
import numpy as np
import torch
import whisper
from typing import Optional, Callable, Dict, Any

from .audio_buffer import AudioBuffer
from .vad_state_machine import VADStateMachine, SpeechState

logger = logging.getLogger(__name__)


class AudioProcessor:
    """
    Main audio processor for VAD-based transcription.
    
    Coordinates VAD analysis, audio buffering, and Whisper transcription
    to provide accurate sentence-level transcription.
    """
    
    def __init__(
        self,
        sample_rate: int = 44100,  # Actual audio sample rate: 44.1kHz
        chunk_duration_ms: int = 200,
        rolling_buffer_duration: float = 4.0,
        silence_threshold_ms: int = 800,
        whisper_model: str = "base",
        vad_threshold: float = 0.5,
        vad_sample_rate: int = 16000,  # VAD model expects 16kHz
    ):
        """
        Initialize the audio processor.

        Args:
            sample_rate: Audio sample rate in Hz (actual: 44.1kHz)
            chunk_duration_ms: Duration of each audio chunk in milliseconds
            rolling_buffer_duration: Duration of rolling buffer for VAD in seconds
            silence_threshold_ms: Silence duration to end speech in milliseconds
            whisper_model: Whisper model size ("tiny", "base", "small", "medium", "large")
            vad_threshold: VAD confidence threshold (0.0 to 1.0)
            vad_sample_rate: Sample rate for VAD model (8kHz or 16kHz)
        """
        self.sample_rate = sample_rate
        self.chunk_duration_ms = chunk_duration_ms
        self.vad_threshold = vad_threshold
        self.vad_sample_rate = vad_sample_rate
        self.downsample_factor = sample_rate // vad_sample_rate
        
        # Initialize components
        self.audio_buffer = AudioBuffer(
            sample_rate=sample_rate,
            rolling_buffer_duration=rolling_buffer_duration,
            chunk_duration_ms=chunk_duration_ms
        )
        
        self.vad_state_machine = VADStateMachine(
            silence_threshold_ms=silence_threshold_ms,
            chunk_duration_ms=chunk_duration_ms
        )
        
        # Set up state machine callbacks
        self.vad_state_machine.set_callbacks(
            on_speech_start=self._on_speech_start,
            on_speech_end=self._on_speech_end
        )
        
        # Initialize models (will be loaded lazily)
        self.vad_model = None
        self.whisper_model = None
        self.whisper_model_name = whisper_model
        
        # Callbacks
        self.on_transcription: Optional[Callable[[str, Dict[str, Any]], None]] = None
        
        # Statistics
        self.stats = {
            "chunks_processed": 0,
            "speech_segments_detected": 0,
            "transcriptions_completed": 0,
            "total_speech_duration": 0.0
        }
        
        logger.info(
            f"AudioProcessor initialized: {sample_rate}Hz -> {vad_sample_rate}Hz (VAD), "
            f"downsample_factor={self.downsample_factor}, "
            f"chunk_duration={chunk_duration_ms}ms, "
            f"whisper_model={whisper_model}, "
            f"vad_threshold={vad_threshold}"
        )

        self.initialize_models()

    def initialize_models(self):
        """Initialize VAD and Whisper models asynchronously."""
        try:
            # Load silero VAD model
            logger.info("Loading silero VAD model...")
            self.vad_model, _ = torch.hub.load(
                repo_or_dir='snakers4/silero-vad',
                model='silero_vad',
                force_reload=False,
                onnx=False
            )
            self.vad_model.eval()
            
            # Load Whisper model
            logger.info(f"Loading Whisper model: {self.whisper_model_name}")
            self.whisper_model = whisper.load_model(self.whisper_model_name)
            
            logger.info("Models loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading models: {e}")
            raise
    
    def set_transcription_callback(self, callback: Callable[[str, Dict[str, Any]], None]):
        """
        Set callback function for transcription results.
        
        Args:
            callback: Function that takes (transcription_text, metadata)
        """
        self.on_transcription = callback
    
    async def process_audio_chunk(self, pcm_data: bytes) -> None:
        """
        Process a new audio chunk.
        
        Args:
            pcm_data: Raw PCM audio data (16-bit LE)
        """
        if self.vad_model is None:
            logger.warning("VAD model not initialized, skipping chunk")
            return
        
        try:
            # Add chunk to buffer
            self.audio_buffer.add_chunk(pcm_data)
            self.stats["chunks_processed"] += 1
            
            # Get rolling buffer for VAD analysis
            rolling_audio = self.audio_buffer.get_rolling_buffer_audio()
            if rolling_audio is None:
                return
            
            # Run VAD on rolling buffer
            has_speech = await self._run_vad(rolling_audio)
            
            # Update state machine
            self.vad_state_machine.update(has_speech)
            
            logger.debug(
                f"Processed chunk: VAD={has_speech}, "
                f"state={self.vad_state_machine.get_current_state().value}"
            )
            
        except Exception as e:
            logger.error(f"Error processing audio chunk: {e}")
    
    async def _run_vad(self, audio_array: np.ndarray) -> bool:
        """
        Run VAD on audio array.

        Args:
            audio_array: Audio data as numpy array (at original sample rate)

        Returns:
            True if speech is detected
        """
        try:
            # Downsample to VAD sample rate (44.1kHz -> 16kHz)
            if self.downsample_factor > 1:
                # Simple downsampling by taking every Nth sample
                downsampled_audio = audio_array[::self.downsample_factor]
            else:
                downsampled_audio = audio_array

            # VAD model expects exactly 512 samples for 16kHz (or 256 for 8kHz)
            expected_samples = 512 if self.vad_sample_rate == 16000 else 256

            # If we have more samples than expected, take the last N samples
            # If we have fewer, pad with zeros
            if len(downsampled_audio) > expected_samples:
                # Take the most recent samples
                vad_audio = downsampled_audio[-expected_samples:]
            elif len(downsampled_audio) < expected_samples:
                # Pad with zeros at the beginning
                padding = expected_samples - len(downsampled_audio)
                vad_audio = np.pad(downsampled_audio, (padding, 0), mode='constant', constant_values=0)
            else:
                vad_audio = downsampled_audio

            # Convert to float32 and normalize
            audio_float = vad_audio.astype(np.float32) / 32768.0

            # Convert to torch tensor and add batch dimension
            audio_tensor = torch.from_numpy(audio_float).unsqueeze(0)

            # Run VAD with the correct sample rate
            with torch.no_grad():
                speech_prob = self.vad_model(audio_tensor, self.vad_sample_rate).item()

            has_speech = speech_prob > self.vad_threshold

            logger.debug(f"VAD result: probability={speech_prob:.3f}, has_speech={has_speech} (processed {len(audio_array)} -> {len(downsampled_audio)} -> {len(vad_audio)} samples)")
            return has_speech

        except Exception as e:
            logger.error(f"Error running VAD: {e}")
            return False
    
    def _on_speech_start(self):
        """Callback for speech start event."""
        logger.info("Speech started - beginning utterance collection")
        self.stats["speech_segments_detected"] += 1
    
    def _on_speech_end(self):
        """Callback for speech end event."""
        logger.info("Speech ended - starting transcription")
        
        # Get accumulated speech audio
        speech_audio = self.audio_buffer.get_speech_buffer_audio()
        if speech_audio is None:
            logger.warning("No speech audio to transcribe")
            return
        
        # Update statistics
        speech_duration = self.audio_buffer.get_speech_buffer_duration()
        self.stats["total_speech_duration"] += speech_duration
        
        logger.info(f"Transcribing {speech_duration:.2f}s of speech")
        
        # Start transcription in background
        asyncio.create_task(self._transcribe_speech(speech_audio, speech_duration))
        
        # Clear speech buffer
        self.audio_buffer.clear_speech_buffer()
    
    async def _transcribe_speech(self, audio_array: np.ndarray, duration: float):
        """
        Transcribe speech audio using Whisper.
        
        Args:
            audio_array: Audio data as numpy array
            duration: Duration of audio in seconds
        """
        if self.whisper_model is None:
            logger.error("Whisper model not initialized")
            return
        
        try:
            # Convert to float32 and normalize for Whisper
            audio_float = audio_array.astype(np.float32) / 32768.0
            
            # Resample to 16kHz if needed (Whisper expects 16kHz)
            if self.sample_rate != 16000:
                # Simple downsampling (for production, use proper resampling)
                downsample_factor = self.sample_rate // 16000
                audio_float = audio_float[::downsample_factor]
            
            # Run Whisper transcription
            result = self.whisper_model.transcribe(
                audio_float,
                language="en",  # Can be made configurable
                task="transcribe"
            )
            
            transcription = result["text"].strip()
            
            if transcription:
                logger.info(f"Transcription: '{transcription}'")
                
                # Prepare metadata
                metadata = {
                    "duration": duration,
                    "confidence": result.get("confidence", 0.0),
                    "language": result.get("language", "en"),
                    "segments": len(result.get("segments", []))
                }
                
                # Call transcription callback
                if self.on_transcription:
                    try:
                        self.on_transcription(transcription, metadata)
                    except Exception as e:
                        logger.error(f"Error in transcription callback: {e}")
                
                self.stats["transcriptions_completed"] += 1
            else:
                logger.info("Empty transcription result")
                
        except Exception as e:
            logger.error(f"Error during transcription: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get processor statistics.
        
        Returns:
            Dictionary with processing statistics
        """
        return {
            **self.stats,
            "audio_buffer": self.audio_buffer.get_stats(),
            "vad_state_machine": self.vad_state_machine.get_stats(),
            "models_loaded": {
                "vad": self.vad_model is not None,
                "whisper": self.whisper_model is not None
            }
        }
    
    def reset(self):
        """Reset the processor state."""
        self.audio_buffer.clear_speech_buffer()
        self.vad_state_machine.reset()
        logger.info("Audio processor reset")
