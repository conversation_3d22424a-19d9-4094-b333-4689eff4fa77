# Ambient AI Server

**Ambient AI Server** is a real-time multimodal ingest system designed to receive, store, and replay streams of sensor data from a single iOS device. It provides a scalable WebSocket interface for collecting and recording a globally ordered transcript of audio, video, IMU, location, and server-generated text. The server is optimized for low-latency recording, durable storage, and offline replay, serving as the central nervous system of an ambient computing stack.

---

## Features

- **WebSocket API**: Ingests real-time `TranscriptChunk` messages from a single iOS client
- **Transcript Engine**: Maintains a globally ordered, append-only timeline of multimodal data
- **Parquet Storage**: Streams data directly to disk using Snappy-compressed Parquet files
- **Live Analytics**: Tracks packet count, modality frequency, and active timestamps
- **Replay CLI**: Replays any saved session transcript over WebSocket for testing or inference
- **Asynchronous & Resilient**: Queue-based architecture decouples ingest from disk I/O
- **Python Protobuf Support**: High-level utilities for serializing/deserializing transcript data

---

## Responsibilities

### 1. Accepts Real-Time Data

- FastAPI WebSocket endpoint at `/ws`
- Receives serialized Protobuf `TranscriptChunk` messages
- Each chunk contains:
  - A data type (audio, video, IMU, etc.)
  - Start and end client-side timestamps
  - A payload (e.g., compressed image, raw PCM audio)

### 2. Appends to the Transcript

- Chunks are appended to the `Transcript` in **arrival order**
- No sorting is performed; this ordering is preserved for replay

### 3. Persists Data to Disk

- Chunks are written to Snappy-compressed **Parquet** files in real time
- One file per session, named with a timestamp

### 4. Tracks Analytics

- Tracks:
  - Total chunks received
  - Per-modality chunk counts
  - Timestamps of recent activity

### 5. Supports Offline Replay

- CLI tool can replay any `.parquet` transcript via WebSocket
- Can preserve original timing or send all data quickly (`--fast` option)

---

## Setup Instructions

### Prerequisites

This project uses [`uv`](https://github.com/astral-sh/uv) for package management and virtual environment handling.

You'll also need the Protocol Buffers compiler (`protoc`):
- **macOS**: `brew install protobuf`
- **Ubuntu/Debian**: `sudo apt-get install protobuf-compiler`

### 1. Install Dependencies

```bash
cd ambient-ai-server
uv sync
```

### 2. Generate Python Protobuf Files

The server uses Python protobuf classes generated from the shared `transcript.proto` schema:

```bash
uv run python generate_protos.py
```

This generates `src/ambient_ai_server/protos/transcript_pb2.py` from `../protos/transcript.proto`.

### 3. Install Python Dependencies

```bash
pip install fastapi uvicorn websockets pandas pyarrow python-multipart protobuf aiohttp
```

Or install from the project requirements:
```bash
pip install -e .
```

### 4. Run the Server

**Option 1: Using the startup script (recommended)**
```bash
./start_server.sh
```

**Option 2: Manual startup**
```bash
python main.py
```

The server will be available at:
- **WebSocket endpoint**: `ws://localhost:8000/ws`
- **Health check**: `http://localhost:8000/health`
- **Analytics**: `http://localhost:8000/analytics`
- **Sessions**: `http://localhost:8000/sessions`

### 5. Test the Setup

**Test protobuf utilities:**
```bash
python test_transcript.py
```

**Test WebSocket functionality:**
```bash
python test_websocket.py
```

This verifies that the protobuf generation, transcript utilities, and WebSocket endpoints are working correctly.

---

## Working with Protobuf Data

The server provides high-level utilities for working with transcript protobuf data:

```python
from src.ambient_ai_server.transcript_utils import TranscriptHandler

# Initialize handler
handler = TranscriptHandler()

# Create different types of chunks
audio_chunk = handler.create_audio_chunk(
    pcm_data=your_audio_bytes,
    client_start_time=start_ms,
    client_end_time=end_ms
)

video_chunk = handler.create_video_chunk(
    jpeg_data=your_jpeg_bytes,
    camera_type="front",  # or "ultrawide"
    client_start_time=start_ms,
    client_end_time=end_ms
)

# Parse any chunk back to a Python dict
parsed = handler.parse_chunk(audio_chunk)
print(f"Type: {parsed['type']}")
print(f"Payload: {parsed['payload']}")
```

### Supported Data Types

- **Audio**: 16-bit LE PCM, 48kHz mono, 200ms intervals
- **Video**: JPEG encoded frames from front or ultrawide camera
- **IMU**: Accelerometer, gyroscope, and gravity data
- **Location**: GPS coordinates, altitude, speed, and accuracy
- **Text**: UTF-8 server-generated messages

---

## API Endpoints

### WebSocket Endpoint
- **URL**: `ws://localhost:8000/ws`
- **Purpose**: Receives binary protobuf `TranscriptChunk` messages
- **Response**: Text acknowledgments and session information

### HTTP Endpoints
- **GET `/health`**: Server health check
- **GET `/analytics`**: Real-time analytics and performance metrics
- **GET `/sessions`**: List all active and recorded sessions
- **POST `/sessions/{session_id}/end`**: Manually end a session

## Replaying a Saved Transcript

**Analyze a session:**
```bash
python -m transcript.replay_client recordings/session.parquet --analyze
```

**Replay a session:**
```bash
python -m transcript.replay_client recordings/session.parquet
```

**Fast replay (no timing delays):**
```bash
python -m transcript.replay_client recordings/session.parquet --fast
```

---

## Architecture

The server consists of several key components:

### Core Components
- **FastAPI Server** (`server.py`): Main application with WebSocket and HTTP endpoints
- **Transcript Manager** (`transcript.py`): Handles session management and Parquet storage
- **Analytics Tracker** (`analytics.py`): Real-time performance monitoring and statistics
- **Replay Client** (`replay_client.py`): Tool for replaying recorded sessions

### Data Flow
1. **Ingest**: WebSocket receives binary protobuf chunks from iOS client
2. **Process**: Server timestamps chunks and validates protobuf format
3. **Store**: Chunks are written to Snappy-compressed Parquet files in real-time
4. **Analytics**: Performance metrics and modality statistics are tracked
5. **Replay**: Recorded sessions can be replayed for testing or analysis

### Storage Format
- **Format**: Snappy-compressed Parquet files
- **Schema**: Session ID, chunk index, type, timestamps, and binary data
- **Location**: `recordings/` directory with timestamp-based filenames

## Development Notes

- Generated protobuf files (`*_pb2.py`) are in `.gitignore` and must be regenerated after any changes to `transcript.proto`
- The `TranscriptHandler` class provides type-safe methods for all supported data modalities
- All timestamps are in UNIX milliseconds for consistency
- The server maintains global ordering based on arrival time, not client timestamps
- Protobuf files are generated with protoc 29.3+ for compatibility with Python protobuf 6.x

