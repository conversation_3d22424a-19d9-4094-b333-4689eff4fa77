# Install required packages (run once)
# !pip install pandas pyarrow torch torchaudio numpy matplotlib seaborn ipywidgets soundfile librosa

import pandas as pd
import numpy as np
import torch
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import soundfile as sf
import librosa
import librosa.display
from IPython.display import Audio, display, HTML
import ipywidgets as widgets
from datetime import datetime, timedelta
import sys
import os

# Add the server source to path for imports
sys.path.insert(0, 'src')
from ambient_ai_server.protos import transcript_pb2

# Set up plotting style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")
%matplotlib inline

# Configuration
SAMPLE_RATE = 44100  # 44.1kHz as specified
CHUNK_DURATION_MS = 200  # 200ms chunks
VAD_SAMPLE_RATE = 16000  # VAD model expects 16kHz
VAD_THRESHOLD = 0.5  # Speech detection threshold
DOWNSAMPLE_FACTOR = SAMPLE_RATE // VAD_SAMPLE_RATE  # ~2.76, we'll use 3 for safety

print(f"Audio Configuration:")
print(f"  Sample Rate: {SAMPLE_RATE} Hz")
print(f"  Chunk Duration: {CHUNK_DURATION_MS} ms")
print(f"  VAD Sample Rate: {VAD_SAMPLE_RATE} Hz")
print(f"  Downsample Factor: {DOWNSAMPLE_FACTOR}")
print(f"  VAD Threshold: {VAD_THRESHOLD}")

# Load Silero VAD model
print("Loading Silero VAD model...")
vad_model, _ = torch.hub.load(
    repo_or_dir='snakers4/silero-vad',
    model='silero_vad',
    force_reload=False,
    onnx=False
)
vad_model.eval()
print("✅ VAD model loaded successfully!")

def load_session_data(parquet_file):
    """Load and parse session data from parquet file."""
    print(f"Loading session data from: {parquet_file}")
    
    # Load the parquet file
    df = pd.read_parquet(parquet_file)
    
    print(f"Session info:")
    print(f"  Total chunks: {len(df)}")
    print(f"  Chunk types: {df['chunk_type'].value_counts().to_dict()}")
    print(f"  Duration: {(df['client_timestamp_end'].max() - df['client_timestamp_start'].min()) / 1000:.1f} seconds")
    
    return df

def extract_audio_chunks(df):
    """Extract audio chunks from session data."""
    # Filter for audio chunks
    audio_df = df[df['chunk_type'] == 'AUDIO'].copy().sort_values('chunk_index')
    
    print(f"Found {len(audio_df)} audio chunks")
    
    audio_chunks = []
    timestamps = []
    
    for _, row in audio_df.iterrows():
        # Parse the chunk
        chunk = transcript_pb2.TranscriptChunk()
        chunk.ParseFromString(row['chunk_data'])
        
        # Parse the audio data
        audio_data = transcript_pb2.AudioData()
        audio_data.ParseFromString(chunk.payload)
        
        # Convert PCM bytes to numpy array
        pcm_array = np.frombuffer(audio_data.pcm, dtype=np.int16)
        audio_chunks.append(pcm_array)
        timestamps.append(chunk.client_timestamp_start)
    
    return audio_chunks, timestamps

def run_vad_analysis(audio_chunks):
    """Run VAD analysis on audio chunks."""
    print("Running VAD analysis...")
    
    vad_results = []
    vad_probabilities = []
    
    for i, chunk in enumerate(audio_chunks):
        # Downsample to 16kHz for VAD
        downsampled = chunk[::DOWNSAMPLE_FACTOR]
        
        # VAD expects exactly 512 samples for 16kHz
        expected_samples = 512
        if len(downsampled) > expected_samples:
            vad_audio = downsampled[-expected_samples:]
        elif len(downsampled) < expected_samples:
            padding = expected_samples - len(downsampled)
            vad_audio = np.pad(downsampled, (padding, 0), mode='constant', constant_values=0)
        else:
            vad_audio = downsampled
        
        # Convert to float32 and normalize
        audio_float = vad_audio.astype(np.float32) / 32768.0
        
        # Run VAD
        with torch.no_grad():
            audio_tensor = torch.from_numpy(audio_float).unsqueeze(0)
            speech_prob = vad_model(audio_tensor, VAD_SAMPLE_RATE).item()
        
        has_speech = speech_prob > VAD_THRESHOLD
        vad_results.append(has_speech)
        vad_probabilities.append(speech_prob)
        
        if (i + 1) % 10 == 0:
            print(f"  Processed {i + 1}/{len(audio_chunks)} chunks")
    
    print(f"✅ VAD analysis complete!")
    print(f"  Speech detected in {sum(vad_results)}/{len(vad_results)} chunks")
    
    return vad_results, vad_probabilities

def detect_speech_segments(vad_results, timestamps, min_speech_duration_ms=400, min_silence_duration_ms=800):
    """Detect continuous speech segments from VAD results."""
    print("Detecting speech segments...")
    
    segments = []
    current_segment = None
    
    min_speech_chunks = min_speech_duration_ms // CHUNK_DURATION_MS
    min_silence_chunks = min_silence_duration_ms // CHUNK_DURATION_MS
    
    speech_chunk_count = 0
    silence_chunk_count = 0
    
    for i, (has_speech, timestamp) in enumerate(zip(vad_results, timestamps)):
        if has_speech:
            speech_chunk_count += 1
            silence_chunk_count = 0
            
            # Start new segment if we have enough speech
            if current_segment is None and speech_chunk_count >= min_speech_chunks:
                current_segment = {
                    'start_chunk': i - speech_chunk_count + 1,
                    'start_time': timestamps[i - speech_chunk_count + 1],
                    'chunks': []
                }
            
            # Add to current segment
            if current_segment is not None:
                current_segment['chunks'].append(i)
        else:
            silence_chunk_count += 1
            speech_chunk_count = 0
            
            # End current segment if we have enough silence
            if current_segment is not None and silence_chunk_count >= min_silence_chunks:
                current_segment['end_chunk'] = current_segment['chunks'][-1]
                current_segment['end_time'] = timestamps[current_segment['chunks'][-1]] + CHUNK_DURATION_MS
                current_segment['duration_ms'] = current_segment['end_time'] - current_segment['start_time']
                segments.append(current_segment)
                current_segment = None
    
    # Close any remaining segment
    if current_segment is not None:
        current_segment['end_chunk'] = current_segment['chunks'][-1]
        current_segment['end_time'] = timestamps[current_segment['chunks'][-1]] + CHUNK_DURATION_MS
        current_segment['duration_ms'] = current_segment['end_time'] - current_segment['start_time']
        segments.append(current_segment)
    
    print(f"✅ Found {len(segments)} speech segments")
    for i, seg in enumerate(segments):
        print(f"  Segment {i+1}: {seg['duration_ms']/1000:.1f}s ({len(seg['chunks'])} chunks)")
    
    return segments

# Specify the session file path
session_file = Path("/Users/<USER>/ambient-ai-prototype/recordings/20250612_235253_session.parquet")

# You can change this to analyze a different session
# session_file = Path("path/to/your/session.parquet")

if not session_file.exists():
    print(f"❌ Session file not found: {session_file}")
    print("Please update the session_file path above to point to your session.parquet file")
else:
    # Load session data
    df = load_session_data(session_file)
    
    # Extract audio chunks
    audio_chunks, timestamps = extract_audio_chunks(df)
    
    if len(audio_chunks) == 0:
        print("❌ No audio chunks found in session")
    else:
        # Run VAD analysis
        vad_results, vad_probabilities = run_vad_analysis(audio_chunks)
        
        # Detect speech segments
        speech_segments = detect_speech_segments(vad_results, timestamps)

def plot_vad_analysis(vad_results, vad_probabilities, timestamps, speech_segments):
    """Plot VAD analysis results and detected speech segments."""
    if len(vad_results) == 0:
        print("No VAD results to plot")
        return
    
    # Convert timestamps to relative seconds
    start_time = timestamps[0]
    time_seconds = [(t - start_time) / 1000 for t in timestamps]
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 8), sharex=True)
    
    # Plot VAD probabilities
    ax1.plot(time_seconds, vad_probabilities, 'b-', alpha=0.7, linewidth=1)
    ax1.axhline(y=VAD_THRESHOLD, color='r', linestyle='--', alpha=0.8, label=f'Threshold ({VAD_THRESHOLD})')
    ax1.fill_between(time_seconds, 0, vad_probabilities, alpha=0.3)
    ax1.set_ylabel('VAD Probability')
    ax1.set_title('Voice Activity Detection Analysis')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(0, 1)
    
    # Plot speech detection (binary)
    speech_binary = [1 if x else 0 for x in vad_results]
    ax2.plot(time_seconds, speech_binary, 'g-', linewidth=2, alpha=0.8)
    ax2.fill_between(time_seconds, 0, speech_binary, alpha=0.4, color='green')
    
    # Highlight detected speech segments
    for i, segment in enumerate(speech_segments):
        start_sec = (segment['start_time'] - start_time) / 1000
        end_sec = (segment['end_time'] - start_time) / 1000
        ax2.axvspan(start_sec, end_sec, alpha=0.3, color='red', 
                   label=f'Speech Segment {i+1}' if i == 0 else '')
    
    ax2.set_ylabel('Speech Detected')
    ax2.set_xlabel('Time (seconds)')
    ax2.set_title('Detected Speech Segments')
    ax2.set_ylim(-0.1, 1.1)
    ax2.grid(True, alpha=0.3)
    if speech_segments:
        ax2.legend()
    
    plt.tight_layout()
    plt.show()
    
    # Print segment details
    if speech_segments:
        print("\n📊 Speech Segment Details:")
        for i, segment in enumerate(speech_segments):
            start_sec = (segment['start_time'] - start_time) / 1000
            end_sec = (segment['end_time'] - start_time) / 1000
            duration_sec = segment['duration_ms'] / 1000
            print(f"  Segment {i+1}: {start_sec:.1f}s - {end_sec:.1f}s (duration: {duration_sec:.1f}s)")
    else:
        print("\n🔇 No speech segments detected")

# Plot the analysis if we have results
if 'vad_results' in locals() and len(vad_results) > 0:
    plot_vad_analysis(vad_results, vad_probabilities, timestamps, speech_segments)

# Create audio players for each detected speech segment
if 'speech_segments' in locals() and len(speech_segments) > 0:
    print(f"🎤 Creating audio players for {len(speech_segments)} speech segments...")
    
    for i, segment in enumerate(speech_segments):
        print(f"\n--- Speech Segment {i+1} ---")
        
        # Extract segment audio
        segment_audio = extract_segment_audio(audio_chunks, segment)
        
        # Create player
        duration_sec = segment['duration_ms'] / 1000
        start_sec = (segment['start_time'] - timestamps[0]) / 1000
        
        title = f"Speech Segment {i+1} (starts at {start_sec:.1f}s, duration: {duration_sec:.1f}s)"
        segment_player = create_audio_player(segment_audio, title)
        display(segment_player)
        
elif 'speech_segments' in locals():
    print("🔇 No speech segments detected - nothing to play back")
else:
    print("❌ No speech analysis available")

def export_audio_segments(audio_chunks, speech_segments, timestamps, output_dir="speech_segments"):
    """Export detected speech segments as audio files."""
    if len(speech_segments) == 0:
        print("🔇 No speech segments to export")
        return
    
    # Create output directory
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    print(f"💾 Exporting {len(speech_segments)} speech segments to {output_path}/")
    
    exported_files = []
    
    for i, segment in enumerate(speech_segments):
        # Extract segment audio
        segment_audio = extract_segment_audio(audio_chunks, segment)
        
        # Create filename
        start_sec = (segment['start_time'] - timestamps[0]) / 1000
        duration_sec = segment['duration_ms'] / 1000
        filename = f"speech_segment_{i+1:02d}_start_{start_sec:.1f}s_duration_{duration_sec:.1f}s.wav"
        filepath = output_path / filename
        
        # Export as WAV file
        sf.write(filepath, segment_audio, SAMPLE_RATE)
        exported_files.append(filepath)
        
        print(f"  ✅ Exported: {filename}")
    
    print(f"\n🎉 Successfully exported {len(exported_files)} audio files!")
    return exported_files

# Export segments if available
if 'speech_segments' in locals() and len(speech_segments) > 0:
    # Create export button
    export_button = widgets.Button(
        description='Export Speech Segments',
        button_style='success',
        tooltip='Export detected speech segments as WAV files'
    )
    
    output_widget = widgets.Output()
    
    def on_export_click(b):
        with output_widget:
            output_widget.clear_output()
            export_audio_segments(audio_chunks, speech_segments, timestamps)
    
    export_button.on_click(on_export_click)
    
    display(widgets.VBox([export_button, output_widget]))
else:
    print("❌ No speech segments available for export")

# Display summary statistics
if 'vad_results' in locals() and 'speech_segments' in locals():
    total_chunks = len(vad_results)
    speech_chunks = sum(vad_results)
    total_duration = len(audio_chunks) * CHUNK_DURATION_MS / 1000
    speech_duration = sum(seg['duration_ms'] for seg in speech_segments) / 1000
    
    print("\n📈 Session Summary:")
    print(f"  Total Duration: {total_duration:.1f} seconds")
    print(f"  Total Audio Chunks: {total_chunks}")
    print(f"  Chunks with Speech: {speech_chunks} ({speech_chunks/total_chunks*100:.1f}%)")
    print(f"  Speech Segments Detected: {len(speech_segments)}")
    print(f"  Total Speech Duration: {speech_duration:.1f} seconds ({speech_duration/total_duration*100:.1f}%)")
    
    if speech_segments:
        avg_segment_duration = speech_duration / len(speech_segments)
        print(f"  Average Segment Duration: {avg_segment_duration:.1f} seconds")
        
        longest_segment = max(speech_segments, key=lambda x: x['duration_ms'])
        shortest_segment = min(speech_segments, key=lambda x: x['duration_ms'])
        print(f"  Longest Segment: {longest_segment['duration_ms']/1000:.1f} seconds")
        print(f"  Shortest Segment: {shortest_segment['duration_ms']/1000:.1f} seconds")
else:
    print("❌ No analysis results available for summary")