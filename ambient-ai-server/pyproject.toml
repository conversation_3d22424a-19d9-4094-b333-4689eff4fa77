[project]
name = "ambient-ai-server"
version = "0.1.0"
description = "Real-time multimodal ingest system for ambient AI data"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "protobuf>=4.24.0",
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "websockets>=12.0",
    "pandas>=2.1.0",
    "pyarrow>=14.0.0",
    "python-multipart>=0.0.6",
    "aiohttp>=3.8.0",
    "torch>=2.0.0",
    "torchaudio>=2.0.0",
    "openai-whisper>=20231117",
    "numpy>=1.24.0",
]

[build-system]
requires = ["setuptools>=61.0", "wheel", "protobuf>=4.24.0"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
"*" = ["*.proto", "py.typed"]
